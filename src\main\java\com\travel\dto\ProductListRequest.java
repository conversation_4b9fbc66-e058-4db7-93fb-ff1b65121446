package com.travel.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 产品列表查询请求DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@ApiModel(description = "产品列表查询请求")
public class ProductListRequest {

    @ApiModelProperty(value = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    @ApiModelProperty(value = "城市ID", example = "1")
    private Integer cityId;

    @ApiModelProperty(value = "产品类型(1讲解包 2景点)", example = "1")
    private Integer type;

    @ApiModelProperty(value = "搜索关键词", example = "故宫")
    private String keyword;
}
