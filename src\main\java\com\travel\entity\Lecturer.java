package com.travel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 讲师信息实体
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lecturer")
@ApiModel(value = "Lecturer对象", description = "讲师信息表")
public class Lecturer {

    @ApiModelProperty(value = "讲师ID")
    @TableId(value = "lecturer_id", type = IdType.AUTO)
    private Integer lecturerId;

    @ApiModelProperty(value = "讲师姓名")
    private String name;

    @ApiModelProperty(value = "讲师头像URL")
    private String avatarUrl;

    @ApiModelProperty(value = "头衔")
    private String title;

    @ApiModelProperty(value = "讲师简介")
    private String intro;

    @ApiModelProperty(value = "专长领域")
    private String expertise;
}
