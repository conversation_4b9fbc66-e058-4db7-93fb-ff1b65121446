package com.travel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.travel.entity.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 产品信息Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 分页查询产品列表（包含城市和讲师信息）
     */
    @Select("SELECT p.*, c.name as city_name, l.name as lecturer_name " +
            "FROM product p " +
            "LEFT JOIN city c ON p.city_id = c.city_id " +
            "LEFT JOIN lecturer l ON p.lecturer_id = l.lecturer_id " +
            "WHERE (#{cityId} IS NULL OR p.city_id = #{cityId}) " +
            "AND (#{type} IS NULL OR p.type = #{type}) " +
            "ORDER BY p.created_at DESC")
    IPage<Product> selectProductPage(Page<Product> page, @Param("cityId") Integer cityId, @Param("type") Integer type);
}
