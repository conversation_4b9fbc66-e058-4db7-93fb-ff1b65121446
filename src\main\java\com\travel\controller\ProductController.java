package com.travel.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.travel.common.Result;
import com.travel.dto.ProductListRequest;
import com.travel.entity.Product;
import com.travel.service.ProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 产品控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/products")
@Api(tags = "产品管理")
@Validated
public class ProductController {

    @Autowired
    private ProductService productService;

    @GetMapping("/list")
    @ApiOperation(value = "获取产品列表", notes = "分页查询产品列表，支持按城市、类型、关键词筛选")
    public Result<IPage<Product>> getProductList(@Valid ProductListRequest request) {
        log.info("获取产品列表请求: page={}, size={}, cityId={}, type={}, keyword={}", 
                request.getPage(), request.getSize(), request.getCityId(), request.getType(), request.getKeyword());
        
        IPage<Product> result = productService.getProductList(request);
        
        log.info("获取产品列表成功: total={}, pages={}", result.getTotal(), result.getPages());
        return Result.success("获取成功", result);
    }

    @GetMapping("/{productId}/detail")
    @ApiOperation(value = "获取产品详情", notes = "根据产品ID获取产品详细信息")
    public Result<Product> getProductDetail(
            @ApiParam(value = "产品ID", required = true, example = "1")
            @PathVariable Integer productId) {
        
        log.info("获取产品详情请求: productId={}", productId);
        
        Product product = productService.getProductDetail(productId);
        
        log.info("获取产品详情成功: productId={}, name={}", productId, product.getName());
        return Result.success("获取成功", product);
    }
}
