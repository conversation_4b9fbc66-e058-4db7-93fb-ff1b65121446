package com.travel.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.travel.dto.ProductListRequest;
import com.travel.entity.Product;

/**
 * 产品服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface ProductService {

    /**
     * 分页查询产品列表
     *
     * @param request 查询请求
     * @return 产品分页数据
     */
    IPage<Product> getProductList(ProductListRequest request);

    /**
     * 根据ID获取产品详情
     *
     * @param productId 产品ID
     * @return 产品详情
     */
    Product getProductDetail(Integer productId);
}
