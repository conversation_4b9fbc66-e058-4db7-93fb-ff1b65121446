package com.travel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品信息实体
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("product")
@ApiModel(value = "Product对象", description = "产品信息表")
public class Product {

    @ApiModelProperty(value = "产品ID")
    @TableId(value = "product_id", type = IdType.AUTO)
    private Integer productId;

    @ApiModelProperty(value = "所属城市ID")
    private Integer cityId;

    @ApiModelProperty(value = "产品名称")
    private String name;

    @ApiModelProperty(value = "产品类型(1讲解包 2景点)")
    private Integer type;

    @ApiModelProperty(value = "封面图URL")
    private String coverUrl;

    @ApiModelProperty(value = "产品描述")
    private String description;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "讲解时长")
    private String duration;

    @ApiModelProperty(value = "讲师ID")
    private Integer lecturerId;

    @ApiModelProperty(value = "标签(逗号分隔)")
    private String tags;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
}
