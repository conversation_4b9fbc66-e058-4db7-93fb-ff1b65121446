#!/bin/bash

# 旅游讲解小程序后端服务启动脚本

# 设置Java环境变量
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-8-openjdk}
export PATH=$JAVA_HOME/bin:$PATH

# 设置应用配置
APP_NAME="travel-backend"
APP_VERSION="1.0.0"
JAR_FILE="target/miniprogram-backend-${APP_VERSION}.jar"
PID_FILE="${APP_NAME}.pid"
LOG_FILE="logs/${APP_NAME}.log"

# 创建日志目录
mkdir -p logs

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件不存在: $JAR_FILE"
    echo "请先执行: mvn clean package"
    exit 1
fi

# 检查是否已经运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "应用已经在运行中，PID: $PID"
        exit 1
    else
        echo "删除过期的PID文件: $PID_FILE"
        rm -f $PID_FILE
    fi
fi

# 设置JVM参数
JVM_OPTS="-Xms512m -Xmx1024m"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCTimeStamps"
JVM_OPTS="$JVM_OPTS -Xloggc:logs/gc.log"

# 设置应用参数
APP_OPTS="--spring.profiles.active=${SPRING_PROFILES_ACTIVE:-dev}"

echo "正在启动 $APP_NAME..."
echo "JAR文件: $JAR_FILE"
echo "JVM参数: $JVM_OPTS"
echo "应用参数: $APP_OPTS"

# 启动应用
nohup java $JVM_OPTS -jar $JAR_FILE $APP_OPTS > $LOG_FILE 2>&1 &

# 保存PID
echo $! > $PID_FILE

echo "应用启动成功！"
echo "PID: $(cat $PID_FILE)"
echo "日志文件: $LOG_FILE"
echo "API文档: http://localhost:8080/api/doc.html"

# 等待几秒钟检查启动状态
sleep 5

if ps -p $(cat $PID_FILE) > /dev/null 2>&1; then
    echo "应用运行正常"
else
    echo "应用启动失败，请检查日志文件: $LOG_FILE"
    exit 1
fi
