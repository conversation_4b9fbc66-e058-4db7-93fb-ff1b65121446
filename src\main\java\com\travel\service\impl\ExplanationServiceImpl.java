package com.travel.service.impl;

import com.travel.entity.AttractionCategory;
import com.travel.entity.ExplanationPoint;
import com.travel.exception.BusinessException;
import com.travel.mapper.AttractionCategoryMapper;
import com.travel.mapper.ExplanationPointMapper;
import com.travel.service.ExplanationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 讲解服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class ExplanationServiceImpl implements ExplanationService {

    @Autowired
    private AttractionCategoryMapper categoryMapper;

    @Autowired
    private ExplanationPointMapper pointMapper;

    @Override
    public List<AttractionCategory> getCategoriesByProductId(Integer productId) {
        List<AttractionCategory> categories = categoryMapper.selectByProductId(productId);
        log.info("获取产品分类列表成功: productId={}, count={}", productId, categories.size());
        return categories;
    }

    @Override
    public List<ExplanationPoint> getPointsByCategoryId(Integer categoryId) {
        List<ExplanationPoint> points = pointMapper.selectByCategoryId(categoryId);
        log.info("获取分类讲解点列表成功: categoryId={}, count={}", categoryId, points.size());
        return points;
    }

    @Override
    public List<ExplanationPoint> getPointsByProductId(Integer productId) {
        List<ExplanationPoint> points = pointMapper.selectByProductId(productId);
        log.info("获取产品讲解点列表成功: productId={}, count={}", productId, points.size());
        return points;
    }

    @Override
    public ExplanationPoint getPointDetail(Integer pointId) {
        ExplanationPoint point = pointMapper.selectById(pointId);
        if (point == null) {
            throw new BusinessException("讲解点不存在");
        }
        log.info("获取讲解点详情成功: pointId={}, title={}", pointId, point.getTitle());
        return point;
    }
}
