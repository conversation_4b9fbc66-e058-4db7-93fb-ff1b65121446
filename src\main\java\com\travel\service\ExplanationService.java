package com.travel.service;

import com.travel.entity.AttractionCategory;
import com.travel.entity.ExplanationPoint;

import java.util.List;

/**
 * 讲解服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface ExplanationService {

    /**
     * 根据产品ID获取分类列表
     *
     * @param productId 产品ID
     * @return 分类列表
     */
    List<AttractionCategory> getCategoriesByProductId(Integer productId);

    /**
     * 根据分类ID获取讲解点列表
     *
     * @param categoryId 分类ID
     * @return 讲解点列表
     */
    List<ExplanationPoint> getPointsByCategoryId(Integer categoryId);

    /**
     * 根据产品ID获取所有讲解点
     *
     * @param productId 产品ID
     * @return 讲解点列表
     */
    List<ExplanationPoint> getPointsByProductId(Integer productId);

    /**
     * 根据ID获取讲解点详情
     *
     * @param pointId 讲解点ID
     * @return 讲解点详情
     */
    ExplanationPoint getPointDetail(Integer pointId);
}
