package com.travel.controller;

import com.travel.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/health")
@Api(tags = "系统健康检查")
public class HealthController {

    @GetMapping
    @ApiOperation(value = "健康检查", notes = "检查系统运行状态")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("service", "旅游讲解小程序后端服务");
        data.put("version", "1.0.0");
        
        log.info("健康检查请求");
        return Result.success("系统运行正常", data);
    }
}
