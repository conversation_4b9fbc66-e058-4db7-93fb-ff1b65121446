# 旅游讲解小程序API测试文档

## 测试环境

- **基础URL**: `http://localhost:8080/api`
- **API文档**: `http://localhost:8080/api/doc.html`

## 认证相关API

### 1. 微信小程序登录

```bash
POST /auth/login
Content-Type: application/json

{
  "code": "081234567890",
  "nickname": "张三",
  "avatarUrl": "https://example.com/avatar.jpg",
  "region": "北京市"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "userId": 1,
    "nickname": "张三",
    "avatarUrl": "https://example.com/avatar.jpg",
    "isNewUser": false
  },
  "timestamp": 1641456000000
}
```

### 2. 绑定手机号

```bash
POST /auth/bind-phone?code=phone_code_here
Authorization: Bearer your_jwt_token
```

### 3. 获取用户信息

```bash
GET /auth/profile
Authorization: Bearer your_jwt_token
```

## 城市管理API

### 1. 获取城市列表

```bash
GET /cities
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "cityId": 1,
      "name": "北京",
      "weather": "晴天 15°C",
      "bannerUrl": "https://example.com/beijing-banner.jpg",
      "slogan": "千年古都，魅力北京"
    }
  ]
}
```

### 2. 获取城市详情

```bash
GET /cities/1
```

## 产品管理API

### 1. 获取产品列表

```bash
GET /products/list?page=1&size=10&cityId=1&type=1&keyword=故宫
```

**参数说明**:
- `page`: 页码（默认1）
- `size`: 每页大小（默认10）
- `cityId`: 城市ID（可选）
- `type`: 产品类型，1-讲解包，2-景点（可选）
- `keyword`: 搜索关键词（可选）

### 2. 获取产品详情

```bash
GET /products/1/detail
```

## 讲解管理API

### 1. 获取产品分类列表

```bash
GET /explanation/categories/1
```

### 2. 获取分类讲解点列表

```bash
GET /explanation/points/category/1
```

### 3. 获取产品所有讲解点

```bash
GET /explanation/points/product/1
```

### 4. 获取讲解点详情

```bash
GET /explanation/points/1/detail
```

## 系统监控API

### 1. 健康检查

```bash
GET /health
```

## 测试用例

### 完整的用户流程测试

1. **用户登录**
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "code": "test_code_001",
    "nickname": "测试用户",
    "avatarUrl": "https://example.com/avatar.jpg",
    "region": "北京市"
  }'
```

2. **获取城市列表**
```bash
curl -X GET http://localhost:8080/api/cities
```

3. **获取产品列表**
```bash
curl -X GET "http://localhost:8080/api/products/list?page=1&size=5&cityId=1"
```

4. **获取产品详情**
```bash
curl -X GET http://localhost:8080/api/products/1/detail
```

5. **获取讲解点列表**
```bash
curl -X GET http://localhost:8080/api/explanation/points/product/1
```

6. **获取讲解点详情**
```bash
curl -X GET http://localhost:8080/api/explanation/points/1/detail
```

## 错误处理测试

### 1. 无效Token测试

```bash
curl -X GET http://localhost:8080/api/auth/profile \
  -H "Authorization: Bearer invalid_token"
```

**预期响应**:
```json
{
  "code": 4001,
  "message": "Token无效",
  "timestamp": 1641456000000
}
```

### 2. 参数验证测试

```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "code": "",
    "nickname": "测试用户"
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "登录凭证不能为空",
  "timestamp": 1641456000000
}
```

### 3. 资源不存在测试

```bash
curl -X GET http://localhost:8080/api/products/999/detail
```

**预期响应**:
```json
{
  "code": 5002,
  "message": "产品不存在",
  "timestamp": 1641456000000
}
```

## 性能测试

### 1. 并发请求测试

使用Apache Bench进行并发测试：

```bash
# 测试产品列表接口
ab -n 1000 -c 10 http://localhost:8080/api/products/list

# 测试城市列表接口
ab -n 1000 -c 10 http://localhost:8080/api/cities
```

### 2. 限流测试

快速发送多个请求测试限流功能：

```bash
for i in {1..150}; do
  curl -X GET http://localhost:8080/api/cities &
done
wait
```

## 数据库测试

### 1. 初始化测试数据

```bash
mysql -u root -p travel < init-data.sql
```

### 2. 验证数据

```sql
-- 检查城市数据
SELECT COUNT(*) FROM city;

-- 检查产品数据
SELECT COUNT(*) FROM product;

-- 检查讲解点数据
SELECT COUNT(*) FROM explanation_point;
```

## 注意事项

1. **微信API配置**: 测试环境需要配置有效的微信小程序AppID和AppSecret
2. **数据库连接**: 确保MySQL和Redis服务正常运行
3. **JWT密钥**: 生产环境请使用强密钥
4. **限流配置**: 可根据实际需求调整限流参数
5. **日志监控**: 关注应用日志，及时发现问题

## 常见问题

### Q1: 启动时数据库连接失败
**A**: 检查数据库配置和服务状态，确保MySQL正在运行且配置正确。

### Q2: Swagger文档无法访问
**A**: 检查Knife4j配置，确保相关依赖正确引入。

### Q3: JWT Token验证失败
**A**: 检查JWT密钥配置和Token格式，确保请求头格式正确。

### Q4: 微信登录失败
**A**: 检查微信小程序配置，确保AppID和AppSecret正确。
