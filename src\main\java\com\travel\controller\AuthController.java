package com.travel.controller;

import com.travel.common.Result;
import com.travel.dto.LoginRequest;
import com.travel.dto.LoginResponse;
import com.travel.service.AuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 认证控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Api(tags = "认证管理")
@Validated
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    @ApiOperation(value = "微信小程序登录", notes = "使用微信登录凭证进行登录，返回JWT token")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        log.info("用户登录请求: code={}", request.getCode());
        
        LoginResponse response = authService.login(request);
        
        log.info("用户登录成功: userId={}, isNewUser={}", response.getUserId(), response.getIsNewUser());
        return Result.success("登录成功", response);
    }

    @PostMapping("/bind-phone")
    @ApiOperation(value = "绑定手机号", notes = "绑定微信用户手机号")
    public Result<Void> bindPhone(
            @ApiParam(value = "手机号获取凭证", required = true)
            @NotBlank(message = "手机号获取凭证不能为空")
            @RequestParam String code,
            HttpServletRequest request) {
        
        Integer userId = (Integer) request.getAttribute("userId");
        log.info("用户绑定手机号请求: userId={}", userId);
        
        boolean success = authService.bindPhone(userId, code);
        
        if (success) {
            log.info("用户绑定手机号成功: userId={}", userId);
            return Result.success("绑定手机号成功");
        } else {
            log.error("用户绑定手机号失败: userId={}", userId);
            return Result.error("绑定手机号失败");
        }
    }

    @GetMapping("/profile")
    @ApiOperation(value = "获取用户信息", notes = "获取当前登录用户的基本信息")
    public Result<Object> getProfile(HttpServletRequest request) {
        Integer userId = (Integer) request.getAttribute("userId");
        String openid = (String) request.getAttribute("openid");
        
        log.info("获取用户信息: userId={}, openid={}", userId, openid);
        
        // 这里可以返回更详细的用户信息
        return Result.success("获取成功", new Object() {
            public Integer getUserId() { return userId; }
            public String getOpenid() { return openid; }
        });
    }
}
