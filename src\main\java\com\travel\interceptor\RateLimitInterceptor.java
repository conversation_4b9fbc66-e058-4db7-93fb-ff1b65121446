package com.travel.interceptor;

import com.alibaba.fastjson.JSON;
import com.travel.common.Result;
import com.travel.common.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 限流拦截器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Component
public class RateLimitInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${rate-limit.enabled:true}")
    private boolean rateLimitEnabled;

    @Value("${rate-limit.default-limit:100}")
    private int defaultLimit;

    @Value("${rate-limit.login-limit:10}")
    private int loginLimit;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!rateLimitEnabled) {
            return true;
        }

        String clientIp = getClientIp(request);
        String requestUri = request.getRequestURI();
        
        // 根据接口类型设置不同的限流规则
        int limit = getLimit(requestUri);
        String key = "rate_limit:" + clientIp + ":" + requestUri;

        try {
            Long count = redisTemplate.opsForValue().increment(key);
            if (count == 1) {
                redisTemplate.expire(key, 1, TimeUnit.MINUTES);
            }

            if (count > limit) {
                log.warn("IP {} 访问接口 {} 超过限流阈值: {}/{}", clientIp, requestUri, count, limit);
                writeErrorResponse(response, ResultCode.RATE_LIMIT_EXCEEDED);
                return false;
            }

            log.debug("IP {} 访问接口 {} 当前计数: {}/{}", clientIp, requestUri, count, limit);
            return true;
        } catch (Exception e) {
            log.error("限流检查异常", e);
            // 异常情况下放行，避免影响正常业务
            return true;
        }
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 根据接口类型获取限流阈值
     */
    private int getLimit(String requestUri) {
        if (requestUri.contains("/auth/login")) {
            return loginLimit;
        }
        return defaultLimit;
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, ResultCode resultCode) throws IOException {
        response.setStatus(429); // Too Many Requests
        response.setContentType("application/json;charset=UTF-8");
        
        Result<Void> result = Result.error(resultCode);
        response.getWriter().write(JSON.toJSONString(result));
    }
}
