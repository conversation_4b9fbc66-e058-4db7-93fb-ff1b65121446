package com.travel.service;

import com.travel.dto.LoginRequest;
import com.travel.dto.LoginResponse;

/**
 * 认证服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface AuthService {

    /**
     * 微信小程序登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request);

    /**
     * 绑定手机号
     *
     * @param userId 用户ID
     * @param code 手机号获取凭证
     * @return 是否成功
     */
    boolean bindPhone(Integer userId, String code);
}
