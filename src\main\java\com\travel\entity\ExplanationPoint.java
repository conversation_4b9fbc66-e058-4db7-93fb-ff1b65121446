package com.travel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 讲解点信息实体
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("explanation_point")
@ApiModel(value = "ExplanationPoint对象", description = "讲解点信息表")
public class ExplanationPoint {

    @ApiModelProperty(value = "讲解点ID")
    @TableId(value = "point_id", type = IdType.AUTO)
    private Integer pointId;

    @ApiModelProperty(value = "分类ID")
    private Integer categoryId;

    @ApiModelProperty(value = "讲解点标题")
    private String title;

    @ApiModelProperty(value = "位置标识")
    private String position;

    @ApiModelProperty(value = "讲解时长")
    private String duration;

    @ApiModelProperty(value = "音频URL")
    private String audioUrl;

    @ApiModelProperty(value = "封面图URL")
    private String coverUrl;

    @ApiModelProperty(value = "图片数组(JSON格式)")
    private String images;

    @ApiModelProperty(value = "详细内容")
    private String content;

    @ApiModelProperty(value = "排序序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;
}
