version: '3.8'

services:
  # 旅游讲解后端服务
  travel-backend:
    build: .
    container_name: travel-backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=travel
      - DB_USERNAME=travel_user
      - DB_PASSWORD=travel_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password
      - WECHAT_APP_ID=your_wechat_app_id
      - WECHAT_APP_SECRET=your_wechat_app_secret
      - JWT_SECRET=your_jwt_secret_key_here
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    networks:
      - travel-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: travel-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=travel
      - MYSQL_USER=travel_user
      - MYSQL_PASSWORD=travel_password
      - MYSQL_ROOT_HOST=%
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./travel.sql:/docker-entrypoint-initdb.d/travel.sql
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
    networks:
      - travel-network

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    container_name: travel-redis
    command: redis-server --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - travel-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: travel-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - travel-backend
    restart: unless-stopped
    networks:
      - travel-network

volumes:
  mysql_data:
  redis_data:

networks:
  travel-network:
    driver: bridge
