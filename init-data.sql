-- 旅游讲解小程序测试数据初始化脚本

-- 插入城市数据
INSERT INTO `city` (`city_id`, `name`, `weather`, `banner_url`, `slogan`) VALUES
(1, '北京', '晴天 15°C', 'https://example.com/beijing-banner.jpg', '千年古都，魅力北京'),
(2, '上海', '多云 18°C', 'https://example.com/shanghai-banner.jpg', '东方明珠，时尚之都'),
(3, '西安', '晴天 12°C', 'https://example.com/xian-banner.jpg', '十三朝古都，丝路起点'),
(4, '杭州', '小雨 16°C', 'https://example.com/hangzhou-banner.jpg', '人间天堂，西湖美景');

-- 插入讲师数据
INSERT INTO `lecturer` (`lecturer_id`, `name`, `avatar_url`, `title`, `intro`, `expertise`) VALUES
(1, '张教授', 'https://example.com/lecturer1.jpg', '历史学教授', '北京大学历史系教授，专注于中国古代史研究20余年', '中国古代史,文物考古'),
(2, '李老师', 'https://example.com/lecturer2.jpg', '资深导游', '国家金牌导游，从事旅游行业15年，讲解生动有趣', '旅游文化,民俗风情'),
(3, '王博士', 'https://example.com/lecturer3.jpg', '建筑学博士', '清华大学建筑学院博士，专注于古建筑保护与研究', '古建筑,园林设计'),
(4, '陈老师', 'https://example.com/lecturer4.jpg', '文化学者', '知名文化学者，央视《百家讲坛》主讲人', '传统文化,诗词文学');

-- 插入产品数据
INSERT INTO `product` (`product_id`, `city_id`, `name`, `type`, `cover_url`, `description`, `price`, `duration`, `lecturer_id`, `tags`, `created_at`) VALUES
(1, 1, '故宫深度讲解', 1, 'https://example.com/gugong.jpg', '跟随专业讲师深度游览紫禁城，了解明清两朝历史文化', 68.00, '120分钟', 1, '历史,文化,建筑', NOW()),
(2, 1, '天坛祈年殿', 2, 'https://example.com/tiantan.jpg', '探索明清皇帝祭天的神圣之地，感受古代建筑的智慧', 38.00, '60分钟', 3, '建筑,宗教,历史', NOW()),
(3, 2, '外滩建筑群', 1, 'https://example.com/waitan.jpg', '漫步外滩，聆听万国建筑博览群的历史故事', 58.00, '90分钟', 2, '建筑,历史,近代', NOW()),
(4, 3, '兵马俑奇迹', 1, 'https://example.com/bingmayong.jpg', '走进秦始皇兵马俑博物馆，揭秘千年地下军团', 88.00, '150分钟', 1, '历史,考古,秦朝', NOW()),
(5, 4, '西湖十景', 1, 'https://example.com/xihu.jpg', '游览西湖十景，品味江南诗意与传说', 48.00, '180分钟', 4, '自然,诗词,传说', NOW());

-- 插入景区详情数据
INSERT INTO `attraction_detail` (`detail_id`, `product_id`, `address`, `open_hours`, `map_url`, `entrance_guide`, `video_url`, `video_duration`) VALUES
(1, 1, '北京市东城区景山前街4号', '08:30-17:00（4月-10月）08:30-16:30（11月-3月）', 'https://example.com/gugong-map.jpg', '从天安门广场步行5分钟，或乘坐地铁1号线天安门东站', 'https://example.com/gugong-intro.mp4', '5分钟'),
(2, 2, '北京市东城区天坛路甲1号', '06:00-22:00', 'https://example.com/tiantan-map.jpg', '乘坐地铁5号线天坛东门站，或公交6路、34路', 'https://example.com/tiantan-intro.mp4', '3分钟'),
(3, 3, '上海市黄浦区中山东一路', '全天开放', 'https://example.com/waitan-map.jpg', '地铁2号线、10号线南京东路站，步行10分钟', 'https://example.com/waitan-intro.mp4', '4分钟'),
(4, 4, '陕西省西安市临潼区秦陵北路', '08:30-18:00', 'https://example.com/bingmayong-map.jpg', '西安市区乘坐游5路直达，或高铁临潼东站', 'https://example.com/bingmayong-intro.mp4', '6分钟'),
(5, 5, '浙江省杭州市西湖区', '全天开放', 'https://example.com/xihu-map.jpg', '地铁1号线龙翔桥站，或公交7路、51路', 'https://example.com/xihu-intro.mp4', '4分钟');

-- 插入景区分类数据
INSERT INTO `attraction_category` (`category_id`, `product_id`, `name`, `sort_order`, `created_at`) VALUES
(1, 1, '外朝三大殿', 1, NOW()),
(2, 1, '内廷后三宫', 2, NOW()),
(3, 1, '御花园', 3, NOW()),
(4, 2, '祈年殿', 1, NOW()),
(5, 2, '圜丘坛', 2, NOW()),
(6, 3, '万国建筑', 1, NOW()),
(7, 3, '黄浦江景', 2, NOW()),
(8, 4, '一号坑', 1, NOW()),
(9, 4, '二号坑', 2, NOW()),
(10, 4, '三号坑', 3, NOW()),
(11, 5, '苏堤春晓', 1, NOW()),
(12, 5, '曲院风荷', 2, NOW()),
(13, 5, '断桥残雪', 3, NOW());

-- 插入讲解点数据
INSERT INTO `explanation_point` (`point_id`, `category_id`, `title`, `position`, `duration`, `audio_url`, `cover_url`, `images`, `content`, `sort_order`, `created_at`) VALUES
(1, 1, '太和殿', '外朝中心', '8分钟', 'https://example.com/audio/taihemen.mp3', 'https://example.com/taihemen.jpg', '["https://example.com/taihemen1.jpg","https://example.com/taihemen2.jpg"]', '太和殿，俗称金銮殿，是紫禁城内体量最大、等级最高的建筑物...', 1, NOW()),
(2, 1, '中和殿', '太和殿后', '5分钟', 'https://example.com/audio/zhonghemen.mp3', 'https://example.com/zhonghemen.jpg', '["https://example.com/zhonghemen1.jpg"]', '中和殿是皇帝去太和殿举行大典前稍事休息的地方...', 2, NOW()),
(3, 1, '保和殿', '中和殿后', '6分钟', 'https://example.com/audio/baohemen.mp3', 'https://example.com/baohemen.jpg', '["https://example.com/baohemen1.jpg","https://example.com/baohemen2.jpg"]', '保和殿是每年除夕皇帝赐宴外藩王公的场所...', 3, NOW()),
(4, 2, '乾清宫', '内廷中心', '7分钟', 'https://example.com/audio/qianqinggong.mp3', 'https://example.com/qianqinggong.jpg', '["https://example.com/qianqinggong1.jpg"]', '乾清宫是内廷正殿，是明清两代皇帝的寝宫...', 1, NOW()),
(5, 2, '交泰殿', '乾清宫后', '4分钟', 'https://example.com/audio/jiaotaidian.mp3', 'https://example.com/jiaotaidian.jpg', '["https://example.com/jiaotaidian1.jpg"]', '交泰殿位于乾清宫和坤宁宫之间，含天地交合、安康美满之意...', 2, NOW()),
(6, 4, '祈年殿主体', '天坛中心', '10分钟', 'https://example.com/audio/qiniandian.mp3', 'https://example.com/qiniandian.jpg', '["https://example.com/qiniandian1.jpg","https://example.com/qiniandian2.jpg"]', '祈年殿是天坛的主体建筑，是皇帝祈求五谷丰登的场所...', 1, NOW()),
(7, 8, '兵马俑一号坑', '博物馆东侧', '15分钟', 'https://example.com/audio/yihaokeng.mp3', 'https://example.com/yihaokeng.jpg', '["https://example.com/yihaokeng1.jpg","https://example.com/yihaokeng2.jpg"]', '一号坑是兵马俑坑中最大的一个，东西长230米，南北宽62米...', 1, NOW()),
(8, 11, '苏堤春晓', '西湖西侧', '12分钟', 'https://example.com/audio/sudichunxiao.mp3', 'https://example.com/sudichunxiao.jpg', '["https://example.com/sudichunxiao1.jpg"]', '苏堤春晓是西湖十景之首，春天时堤上桃柳夹岸，红绿间错...', 1, NOW());

-- 插入资讯数据
INSERT INTO `news` (`news_id`, `city_id`, `title`, `content`, `cover_url`, `created_at`) VALUES
(1, 1, '故宫博物院推出数字化展览', '故宫博物院近日推出全新数字化展览，游客可通过VR技术身临其境地体验紫禁城的历史变迁...', 'https://example.com/news1.jpg', NOW()),
(2, 1, '天坛公园春季赏花指南', '春暖花开，天坛公园内各种花卉竞相绽放，为游客提供了绝佳的赏花体验...', 'https://example.com/news2.jpg', NOW()),
(3, 2, '外滩夜景灯光秀时间调整', '为了给游客更好的观赏体验，外滩夜景灯光秀时间进行了调整...', 'https://example.com/news3.jpg', NOW()),
(4, 3, '兵马俑博物馆新发现', '考古学家在兵马俑博物馆附近又有新的重要发现，为秦朝历史研究提供了新的线索...', 'https://example.com/news4.jpg', NOW()),
(5, 4, '西湖荷花节即将开幕', '一年一度的西湖荷花节即将开幕，届时将有精彩的文艺表演和荷花展览...', 'https://example.com/news5.jpg', NOW());

-- 插入测试用户数据（用于开发测试）
INSERT INTO `user` (`user_id`, `openid`, `nickname`, `avatar_url`, `region`, `phone`, `created_at`) VALUES
(1, 'test_openid_001', '测试用户1', 'https://example.com/avatar1.jpg', '北京市', '13800138001', NOW()),
(2, 'test_openid_002', '测试用户2', 'https://example.com/avatar2.jpg', '上海市', '13800138002', NOW());

-- 插入测试订单数据
INSERT INTO `order` (`order_id`, `user_id`, `order_no`, `total_amount`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'TRV202501060001', 68.00, 1, NOW(), NOW()),
(2, 2, 'TRV202501060002', 58.00, 1, NOW(), NOW()),
(3, 1, 'TRV202501060003', 38.00, 0, NOW(), NOW());

-- 插入订单明细数据
INSERT INTO `order_item` (`item_id`, `order_id`, `product_id`, `sku_name`, `price`, `quantity`) VALUES
(1, 1, 1, '故宫深度讲解-成人票', 68.00, 1),
(2, 2, 3, '外滩建筑群-成人票', 58.00, 1),
(3, 3, 2, '天坛祈年殿-成人票', 38.00, 1);

-- 插入用户评价数据
INSERT INTO `review` (`review_id`, `user_id`, `product_id`, `content`, `rating`, `created_at`) VALUES
(1, 1, 1, '讲解非常详细，张教授的专业知识让我对故宫有了更深的了解，强烈推荐！', 5, NOW()),
(2, 2, 3, '外滩的建筑群确实很有特色，导游讲解生动有趣，值得一听。', 4, NOW()),
(3, 1, 2, '天坛的建筑设计真的很精妙，通过讲解了解了很多以前不知道的知识。', 5, NOW());
