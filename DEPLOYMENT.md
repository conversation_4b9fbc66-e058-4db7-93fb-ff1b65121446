# 旅游讲解小程序后端部署指南

## 部署环境要求

### 基础环境
- **操作系统**: Linux (推荐 CentOS 7+ 或 Ubuntu 18.04+)
- **Java**: JDK 1.8+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **反向代理**: Nginx (可选)

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的网络连接

## 部署方式

### 方式一：传统部署

#### 1. 环境准备

**安装Java 8**
```bash
# CentOS
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# Ubuntu
apt-get update
apt-get install -y openjdk-8-jdk

# 验证安装
java -version
```

**安装MySQL 8.0**
```bash
# CentOS
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
rpm -ivh mysql80-community-release-el7-3.noarch.rpm
yum install -y mysql-server

# 启动MySQL
systemctl start mysqld
systemctl enable mysqld

# 获取临时密码
grep 'temporary password' /var/log/mysqld.log

# 安全配置
mysql_secure_installation
```

**安装Redis**
```bash
# CentOS
yum install -y epel-release
yum install -y redis

# Ubuntu
apt-get install -y redis-server

# 启动Redis
systemctl start redis
systemctl enable redis
```

#### 2. 数据库初始化

```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE travel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 创建用户
CREATE USER 'travel_user'@'%' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON travel.* TO 'travel_user'@'%';
FLUSH PRIVILEGES;

# 导入表结构
mysql -u travel_user -p travel < travel.sql

# 导入测试数据（可选）
mysql -u travel_user -p travel < init-data.sql
```

#### 3. 应用部署

```bash
# 创建应用目录
mkdir -p /opt/travel-backend
cd /opt/travel-backend

# 上传JAR包
scp target/miniprogram-backend-1.0.0.jar user@server:/opt/travel-backend/

# 创建配置文件
cat > application-prod.yml << EOF
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: travel_user
    password: your_strong_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password

wechat:
  miniprogram:
    app-id: your_wechat_app_id
    app-secret: your_wechat_app_secret

jwt:
  secret: your_very_strong_jwt_secret_key_here
EOF

# 设置权限
chmod +x start.sh stop.sh
chmod 600 application-prod.yml

# 启动应用
./start.sh
```

#### 4. 配置Nginx（可选）

```bash
# 安装Nginx
yum install -y nginx  # CentOS
apt-get install -y nginx  # Ubuntu

# 配置文件
cat > /etc/nginx/conf.d/travel-backend.conf << EOF
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location / {
        return 200 '{"message":"旅游讲解小程序后端服务","status":"running"}';
        add_header Content-Type application/json;
    }
}
EOF

# 启动Nginx
systemctl start nginx
systemctl enable nginx
```

### 方式二：Docker部署

#### 1. 安装Docker

```bash
# CentOS
yum install -y yum-utils
yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
yum install -y docker-ce docker-ce-cli containerd.io docker-compose

# Ubuntu
apt-get update
apt-get install -y docker.io docker-compose

# 启动Docker
systemctl start docker
systemctl enable docker
```

#### 2. 构建镜像

```bash
# 构建应用镜像
docker build -t travel-backend:1.0.0 .

# 或者使用docker-compose
docker-compose build
```

#### 3. 启动服务

```bash
# 使用docker-compose启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f travel-backend
```

#### 4. 数据初始化

```bash
# 等待MySQL启动完成
sleep 30

# 导入数据
docker-compose exec mysql mysql -u travel_user -ptravel_password travel < /docker-entrypoint-initdb.d/travel.sql
```

### 方式三：云服务部署

#### 阿里云ECS部署

1. **创建ECS实例**
   - 选择合适的配置（2核4G以上）
   - 安装CentOS 7或Ubuntu 18.04
   - 配置安全组开放8080端口

2. **安装环境**
   ```bash
   # 使用一键安装脚本
   curl -fsSL https://get.docker.com | bash -s docker
   curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   chmod +x /usr/local/bin/docker-compose
   ```

3. **部署应用**
   ```bash
   git clone your-repo
   cd travel-backend
   docker-compose up -d
   ```

## 环境变量配置

### 生产环境必需的环境变量

```bash
# 数据库配置
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=travel
export DB_USERNAME=travel_user
export DB_PASSWORD=your_strong_password

# Redis配置
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=your_redis_password

# 微信小程序配置
export WECHAT_APP_ID=your_wechat_app_id
export WECHAT_APP_SECRET=your_wechat_app_secret

# JWT配置
export JWT_SECRET=your_very_strong_jwt_secret_key_here
export JWT_EXPIRATION=604800

# 文件上传配置
export FILE_UPLOAD_PATH=/data/uploads/
```

## 监控和维护

### 1. 日志管理

```bash
# 查看应用日志
tail -f logs/travel-backend.log

# 日志轮转配置
cat > /etc/logrotate.d/travel-backend << EOF
/opt/travel-backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 travel travel
}
EOF
```

### 2. 健康检查

```bash
# 创建健康检查脚本
cat > health-check.sh << EOF
#!/bin/bash
response=\$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/health)
if [ \$response -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy"
    exit 1
fi
EOF

chmod +x health-check.sh

# 添加到crontab
echo "*/5 * * * * /opt/travel-backend/health-check.sh" | crontab -
```

### 3. 备份策略

```bash
# 数据库备份脚本
cat > backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
mysqldump -u travel_user -p'your_password' travel > /backup/travel_\$DATE.sql
find /backup -name "travel_*.sql" -mtime +7 -delete
EOF

chmod +x backup.sh

# 每天凌晨2点备份
echo "0 2 * * * /opt/travel-backend/backup.sh" | crontab -
```

## 性能优化

### 1. JVM调优

```bash
# 修改启动脚本中的JVM参数
JVM_OPTS="-Xms1024m -Xmx2048m"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=/opt/travel-backend/logs/"
```

### 2. 数据库优化

```sql
-- MySQL配置优化
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M
```

### 3. Redis优化

```bash
# Redis配置优化
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 安全配置

### 1. 防火墙配置

```bash
# CentOS 7
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --reload

# Ubuntu
ufw allow 8080/tcp
ufw enable
```

### 2. SSL证书配置

```bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com
```

### 3. 安全加固

```bash
# 禁用不必要的服务
systemctl disable telnet
systemctl disable ftp

# 更新系统
yum update -y  # CentOS
apt-get update && apt-get upgrade -y  # Ubuntu
```

## 故障排除

### 常见问题

1. **应用启动失败**
   - 检查Java版本和环境变量
   - 查看应用日志
   - 验证数据库连接

2. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证用户权限
   - 检查防火墙设置

3. **Redis连接失败**
   - 检查Redis服务状态
   - 验证密码配置
   - 检查网络连接

4. **内存不足**
   - 调整JVM堆内存大小
   - 检查系统内存使用情况
   - 优化应用代码

### 紧急恢复

```bash
# 快速重启服务
./stop.sh && ./start.sh

# 从备份恢复数据库
mysql -u travel_user -p travel < /backup/travel_20250106_020000.sql

# 清理日志文件
find logs/ -name "*.log" -mtime +7 -delete
```

## 联系支持

如遇到部署问题，请联系技术支持：
- 邮箱：<EMAIL>
- 文档：https://github.com/travel/miniprogram-backend
- 问题反馈：https://github.com/travel/miniprogram-backend/issues
