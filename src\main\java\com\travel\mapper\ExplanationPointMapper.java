package com.travel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travel.entity.ExplanationPoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 讲解点信息Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface ExplanationPointMapper extends BaseMapper<ExplanationPoint> {

    /**
     * 根据分类ID查询讲解点列表
     */
    @Select("SELECT * FROM explanation_point WHERE category_id = #{categoryId} ORDER BY sort_order ASC, created_at ASC")
    List<ExplanationPoint> selectByCategoryId(@Param("categoryId") Integer categoryId);

    /**
     * 根据产品ID查询所有讲解点
     */
    @Select("SELECT ep.* FROM explanation_point ep " +
            "INNER JOIN attraction_category ac ON ep.category_id = ac.category_id " +
            "WHERE ac.product_id = #{productId} " +
            "ORDER BY ac.sort_order ASC, ep.sort_order ASC")
    List<ExplanationPoint> selectByProductId(@Param("productId") Integer productId);
}
