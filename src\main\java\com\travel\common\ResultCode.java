package com.travel.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),
    
    // 认证相关
    UNAUTHORIZED(401, "未授权访问"),
    TOKEN_INVALID(4001, "Token无效"),
    TOKEN_EXPIRED(4002, "Token已过期"),
    LOGIN_FAILED(4003, "登录失败"),
    WECHAT_AUTH_FAILED(4004, "微信授权失败"),
    
    // 业务相关
    USER_NOT_FOUND(5001, "用户不存在"),
    PRODUCT_NOT_FOUND(5002, "产品不存在"),
    ORDER_NOT_FOUND(5003, "订单不存在"),
    INSUFFICIENT_BALANCE(5004, "余额不足"),
    ORDER_STATUS_ERROR(5005, "订单状态错误"),
    
    // 系统相关
    RATE_LIMIT_EXCEEDED(4291, "请求过于频繁"),
    DATABASE_ERROR(5101, "数据库操作失败"),
    REDIS_ERROR(5102, "缓存操作失败"),
    FILE_UPLOAD_ERROR(5103, "文件上传失败");

    private final Integer code;
    private final String message;
}
