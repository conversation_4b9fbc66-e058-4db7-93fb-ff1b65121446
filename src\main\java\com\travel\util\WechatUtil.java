package com.travel.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 微信API工具类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Component
public class WechatUtil {

    @Value("${wechat.miniprogram.app-id}")
    private String appId;

    @Value("${wechat.miniprogram.app-secret}")
    private String appSecret;

    @Value("${wechat.miniprogram.login-url}")
    private String loginUrl;

    @Value("${wechat.miniprogram.phone-url}")
    private String phoneUrl;

    private final WebClient webClient;

    public WechatUtil() {
        this.webClient = WebClient.builder().build();
    }

    /**
     * 微信小程序登录，获取session_key和openid
     *
     * @param code 微信登录凭证
     * @return 包含openid和session_key的JSON对象
     */
    public JSONObject login(String code) {
        try {
            String url = String.format("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    loginUrl, appId, appSecret, code);

            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            log.info("微信登录响应: {}", response);
            JSONObject result = JSON.parseObject(response);

            if (result.containsKey("errcode") && result.getInteger("errcode") != 0) {
                log.error("微信登录失败: {}", result.getString("errmsg"));
                return null;
            }

            return result;
        } catch (Exception e) {
            log.error("微信登录异常", e);
            return null;
        }
    }

    /**
     * 获取微信用户手机号
     *
     * @param accessToken 接口调用凭证
     * @param code 手机号获取凭证
     * @return 包含手机号信息的JSON对象
     */
    public JSONObject getPhoneNumber(String accessToken, String code) {
        try {
            String url = String.format("%s?access_token=%s", phoneUrl, accessToken);

            JSONObject requestBody = new JSONObject();
            requestBody.put("code", code);

            String response = webClient.post()
                    .uri(url)
                    .bodyValue(requestBody.toJSONString())
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            log.info("获取手机号响应: {}", response);
            JSONObject result = JSON.parseObject(response);

            if (result.containsKey("errcode") && result.getInteger("errcode") != 0) {
                log.error("获取手机号失败: {}", result.getString("errmsg"));
                return null;
            }

            return result;
        } catch (Exception e) {
            log.error("获取手机号异常", e);
            return null;
        }
    }

    /**
     * 获取微信接口调用凭证
     *
     * @return access_token
     */
    public String getAccessToken() {
        try {
            String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                    appId, appSecret);

            String response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            JSONObject result = JSON.parseObject(response);
            if (result.containsKey("access_token")) {
                return result.getString("access_token");
            } else {
                log.error("获取access_token失败: {}", result.getString("errmsg"));
                return null;
            }
        } catch (Exception e) {
            log.error("获取access_token异常", e);
            return null;
        }
    }
}
