# 旅游讲解小程序后端服务

## 项目简介

这是一个基于Spring Boot 2.x + Java 8开发的微信小程序旅游讲解后端服务，提供完整的用户认证、产品管理、订单管理等功能。

## 技术栈

- **框架**: Spring Boot 2.7.18
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **ORM**: MyBatis-Plus 3.5.3.1
- **API文档**: Knife4j 2.0.9
- **认证**: JWT
- **工具类**: Hutool、FastJSON
- **构建工具**: Maven

## 主要功能

### 1. 微信认证系统
- ✅ 微信小程序登录
- ✅ JWT token生成和验证
- ✅ 用户信息管理
- ✅ 手机号绑定

### 2. 核心业务功能
- ✅ 城市信息管理
- ✅ 产品列表和详情
- ✅ 分页查询和搜索
- ✅ 讲解点管理（数据结构已准备）
- ✅ 订单管理（数据结构已准备）

### 3. 系统功能
- ✅ 统一响应格式
- ✅ 全局异常处理
- ✅ 接口限流
- ✅ 跨域配置
- ✅ 完整的Swagger/Knife4j文档

## 快速开始

### 1. 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 2. 数据库初始化
```sql
# 创建数据库
CREATE DATABASE travel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据表结构
mysql -u root -p travel < travel.sql
```

### 3. 配置文件
修改 `src/main/resources/application.yml` 中的配置：

```yaml
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: your_db_username
    password: your_db_password
  
  redis:
    host: your_redis_host
    port: 6379
    password: your_redis_password

# 微信小程序配置
wechat:
  miniprogram:
    app-id: your_wechat_app_id
    app-secret: your_wechat_app_secret

# JWT配置
jwt:
  secret: your_jwt_secret_key
```

### 4. 启动应用
```bash
mvn clean install
mvn spring-boot:run
```

### 5. 访问API文档
启动成功后访问：http://localhost:8080/api/doc.html

## API接口说明

### 认证相关
- `POST /api/auth/login` - 微信小程序登录
- `POST /api/auth/bind-phone` - 绑定手机号
- `GET /api/auth/profile` - 获取用户信息

### 城市管理
- `GET /api/cities` - 获取城市列表
- `GET /api/cities/{cityId}` - 获取城市详情

### 产品管理
- `GET /api/products/list` - 获取产品列表（支持分页、筛选、搜索）
- `GET /api/products/{productId}/detail` - 获取产品详情

### 系统监控
- `GET /api/health` - 健康检查

## 数据库设计

### 核心表结构
- `user` - 用户信息表
- `city` - 城市信息表
- `product` - 产品信息表
- `attraction_category` - 景区分类表
- `explanation_point` - 讲解点信息表
- `lecturer` - 讲师信息表
- `order` - 订单信息表
- `order_item` - 订单明细表
- `review` - 用户评价表
- `news` - 资讯信息表

## 安全特性

### 1. 认证授权
- JWT token认证
- 接口权限控制
- 用户身份验证

### 2. 接口安全
- 请求参数验证
- SQL注入防护
- XSS攻击防护
- CORS跨域配置

### 3. 限流保护
- IP级别限流
- 接口级别限流
- Redis分布式限流

## 开发规范

### 1. 代码结构
```
src/main/java/com/travel/
├── config/          # 配置类
├── controller/      # 控制器层
├── service/         # 服务层
├── mapper/          # 数据访问层
├── entity/          # 实体类
├── dto/             # 数据传输对象
├── util/            # 工具类
├── exception/       # 异常处理
├── interceptor/     # 拦截器
└── common/          # 公共类
```

### 2. 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1641456000000
}
```

### 3. 错误码规范
- 200: 操作成功
- 400: 参数错误
- 401: 未授权访问
- 404: 资源不存在
- 429: 请求过于频繁
- 500: 系统错误

## 部署说明

### 1. 打包应用
```bash
mvn clean package -Dmaven.test.skip=true
```

### 2. 运行JAR包
```bash
java -jar target/miniprogram-backend-1.0.0.jar
```

### 3. 环境变量配置
```bash
export DB_USERNAME=your_db_username
export DB_PASSWORD=your_db_password
export REDIS_HOST=your_redis_host
export WECHAT_APP_ID=your_app_id
export WECHAT_APP_SECRET=your_app_secret
export JWT_SECRET=your_jwt_secret
```

## 扩展功能

项目已为以下功能预留了数据结构和接口框架：

1. **讲解点管理** - 音频讲解、图片展示
2. **订单系统** - 支付、退款、订单状态管理
3. **评价系统** - 用户评价和评分
4. **资讯管理** - 旅游资讯发布
5. **讲师管理** - 讲师信息和专长

## 联系方式

如有问题，请联系开发团队：<EMAIL>

## 许可证

Apache License 2.0
