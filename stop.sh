#!/bin/bash

# 旅游讲解小程序后端服务停止脚本

APP_NAME="travel-backend"
PID_FILE="${APP_NAME}.pid"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "PID文件不存在: $PID_FILE"
    echo "应用可能没有运行"
    exit 1
fi

# 读取PID
PID=$(cat $PID_FILE)

# 检查进程是否存在
if ! ps -p $PID > /dev/null 2>&1; then
    echo "进程不存在: PID $PID"
    echo "删除PID文件: $PID_FILE"
    rm -f $PID_FILE
    exit 1
fi

echo "正在停止 $APP_NAME (PID: $PID)..."

# 发送TERM信号
kill $PID

# 等待进程结束
for i in {1..30}; do
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "应用已成功停止"
        rm -f $PID_FILE
        exit 0
    fi
    echo "等待应用停止... ($i/30)"
    sleep 1
done

# 如果进程仍然存在，强制杀死
echo "应用未能正常停止，强制终止..."
kill -9 $PID

if ! ps -p $PID > /dev/null 2>&1; then
    echo "应用已强制停止"
    rm -f $PID_FILE
else
    echo "无法停止应用，请手动处理"
    exit 1
fi
