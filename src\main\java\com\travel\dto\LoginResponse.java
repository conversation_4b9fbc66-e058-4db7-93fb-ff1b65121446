package com.travel.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录响应DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@ApiModel(description = "登录响应")
public class LoginResponse {

    @ApiModelProperty(value = "访问令牌", example = "eyJhbGciOiJIUzUxMiJ9...")
    private String token;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickname;

    @ApiModelProperty(value = "用户头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @ApiModelProperty(value = "是否为新用户", example = "false")
    private Boolean isNewUser;
}
