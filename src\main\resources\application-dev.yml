# 开发环境配置
spring:
  datasource:
    url: ************************************************************************************************************************************************
    username: root
    password: 123456
    
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

# 微信小程序配置（开发环境）
wechat:
  miniprogram:
    app-id: wx1234567890abcdef  # 请替换为实际的AppID
    app-secret: your_app_secret_here  # 请替换为实际的AppSecret

# JWT配置（开发环境）
jwt:
  secret: travel_dev_secret_key_2025
  expiration: 604800

# 日志配置（开发环境）
logging:
  level:
    com.travel: debug
    org.springframework.web: debug
    com.baomidou.mybatisplus: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 限流配置（开发环境 - 更宽松）
rate-limit:
  enabled: true
  default-limit: 1000
  login-limit: 100
