package com.travel.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.travel.dto.LoginRequest;
import com.travel.dto.LoginResponse;
import com.travel.entity.User;
import com.travel.exception.BusinessException;
import com.travel.mapper.UserMapper;
import com.travel.service.AuthService;
import com.travel.util.JwtUtil;
import com.travel.util.WechatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 认证服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private WechatUtil wechatUtil;

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public LoginResponse login(LoginRequest request) {
        // 1. 调用微信接口获取openid
        JSONObject wechatResult = wechatUtil.login(request.getCode());
        if (wechatResult == null) {
            throw new BusinessException("微信登录失败");
        }

        String openid = wechatResult.getString("openid");
        if (!StringUtils.hasText(openid)) {
            throw new BusinessException("获取微信用户信息失败");
        }

        // 2. 查询用户是否存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getOpenid, openid);
        User existUser = userMapper.selectOne(queryWrapper);

        boolean isNewUser = false;
        User user;

        if (existUser == null) {
            // 3. 新用户注册
            user = new User();
            user.setOpenid(openid);
            user.setNickname(request.getNickname());
            user.setAvatarUrl(request.getAvatarUrl());
            user.setRegion(request.getRegion());
            user.setCreatedAt(LocalDateTime.now());
            
            userMapper.insert(user);
            isNewUser = true;
            log.info("新用户注册成功: openid={}, userId={}", openid, user.getUserId());
        } else {
            // 4. 更新用户信息
            user = existUser;
            if (StringUtils.hasText(request.getNickname())) {
                user.setNickname(request.getNickname());
            }
            if (StringUtils.hasText(request.getAvatarUrl())) {
                user.setAvatarUrl(request.getAvatarUrl());
            }
            if (StringUtils.hasText(request.getRegion())) {
                user.setRegion(request.getRegion());
            }
            userMapper.updateById(user);
            log.info("用户信息更新成功: openid={}, userId={}", openid, user.getUserId());
        }

        // 5. 生成JWT token
        String token = jwtUtil.generateToken(user.getUserId(), openid);

        // 6. 构造响应
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        response.setUserId(user.getUserId());
        response.setNickname(user.getNickname());
        response.setAvatarUrl(user.getAvatarUrl());
        response.setIsNewUser(isNewUser);

        return response;
    }

    @Override
    public boolean bindPhone(Integer userId, String code) {
        try {
            // 1. 获取access_token
            String accessToken = wechatUtil.getAccessToken();
            if (!StringUtils.hasText(accessToken)) {
                throw new BusinessException("获取微信访问令牌失败");
            }

            // 2. 获取手机号
            JSONObject phoneResult = wechatUtil.getPhoneNumber(accessToken, code);
            if (phoneResult == null) {
                throw new BusinessException("获取手机号失败");
            }

            JSONObject phoneInfo = phoneResult.getJSONObject("phone_info");
            if (phoneInfo == null) {
                throw new BusinessException("手机号信息为空");
            }

            String phoneNumber = phoneInfo.getString("phoneNumber");
            if (!StringUtils.hasText(phoneNumber)) {
                throw new BusinessException("手机号为空");
            }

            // 3. 更新用户手机号
            User user = new User();
            user.setUserId(userId);
            user.setPhone(phoneNumber);
            
            int result = userMapper.updateById(user);
            if (result > 0) {
                log.info("用户手机号绑定成功: userId={}, phone={}", userId, phoneNumber);
                return true;
            } else {
                log.error("用户手机号绑定失败: userId={}", userId);
                return false;
            }
        } catch (Exception e) {
            log.error("绑定手机号异常: userId={}", userId, e);
            throw new BusinessException("绑定手机号失败: " + e.getMessage());
        }
    }
}
