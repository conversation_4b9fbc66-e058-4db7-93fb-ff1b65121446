package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.AttractionCategory;
import com.travel.entity.ExplanationPoint;
import com.travel.service.ExplanationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 讲解控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/explanation")
@Api(tags = "讲解管理")
public class ExplanationController {

    @Autowired
    private ExplanationService explanationService;

    @GetMapping("/categories/{productId}")
    @ApiOperation(value = "获取产品分类列表", notes = "根据产品ID获取景区分类列表")
    public Result<List<AttractionCategory>> getCategories(
            @ApiParam(value = "产品ID", required = true, example = "1")
            @PathVariable Integer productId) {
        
        log.info("获取产品分类列表请求: productId={}", productId);
        
        List<AttractionCategory> categories = explanationService.getCategoriesByProductId(productId);
        
        log.info("获取产品分类列表成功: productId={}, count={}", productId, categories.size());
        return Result.success("获取成功", categories);
    }

    @GetMapping("/points/category/{categoryId}")
    @ApiOperation(value = "获取分类讲解点列表", notes = "根据分类ID获取讲解点列表")
    public Result<List<ExplanationPoint>> getPointsByCategory(
            @ApiParam(value = "分类ID", required = true, example = "1")
            @PathVariable Integer categoryId) {
        
        log.info("获取分类讲解点列表请求: categoryId={}", categoryId);
        
        List<ExplanationPoint> points = explanationService.getPointsByCategoryId(categoryId);
        
        log.info("获取分类讲解点列表成功: categoryId={}, count={}", categoryId, points.size());
        return Result.success("获取成功", points);
    }

    @GetMapping("/points/product/{productId}")
    @ApiOperation(value = "获取产品所有讲解点", notes = "根据产品ID获取所有讲解点列表")
    public Result<List<ExplanationPoint>> getPointsByProduct(
            @ApiParam(value = "产品ID", required = true, example = "1")
            @PathVariable Integer productId) {
        
        log.info("获取产品讲解点列表请求: productId={}", productId);
        
        List<ExplanationPoint> points = explanationService.getPointsByProductId(productId);
        
        log.info("获取产品讲解点列表成功: productId={}, count={}", productId, points.size());
        return Result.success("获取成功", points);
    }

    @GetMapping("/points/{pointId}/detail")
    @ApiOperation(value = "获取讲解点详情", notes = "根据讲解点ID获取详细信息")
    public Result<ExplanationPoint> getPointDetail(
            @ApiParam(value = "讲解点ID", required = true, example = "1")
            @PathVariable Integer pointId) {
        
        log.info("获取讲解点详情请求: pointId={}", pointId);
        
        ExplanationPoint point = explanationService.getPointDetail(pointId);
        
        log.info("获取讲解点详情成功: pointId={}, title={}", pointId, point.getTitle());
        return Result.success("获取成功", point);
    }
}
