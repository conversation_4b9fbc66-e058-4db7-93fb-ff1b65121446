# 生产环境配置
spring:
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:travel}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
    
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    database: ${REDIS_DATABASE:0}
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 5

# 微信小程序配置（生产环境）
wechat:
  miniprogram:
    app-id: ${WECHAT_APP_ID}
    app-secret: ${WECHAT_APP_SECRET}

# JWT配置（生产环境）
jwt:
  secret: ${JWT_SECRET}
  expiration: ${JWT_EXPIRATION:604800}

# 日志配置（生产环境）
logging:
  level:
    com.travel: info
    org.springframework.web: warn
    com.baomidou.mybatisplus: warn
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/travel/travel-backend.log
    max-size: 100MB
    max-history: 30

# 限流配置（生产环境 - 更严格）
rate-limit:
  enabled: true
  default-limit: 100
  login-limit: 10

# 文件上传配置（生产环境）
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/data/uploads/}
    max-size: 10MB
