# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Java
*.class
*.log
*.ctxt
.mtj.tmp/
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

# IDE
.idea/
*.iws
*.iml
*.ipr
.vscode/
*.swp
*.swo
*~

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 应用相关
logs/
uploads/
*.pid
*.log.*

# 配置文件（包含敏感信息）
application-local.yml
application-secret.yml

# 数据库
*.db
*.sqlite
*.sqlite3

# 临时文件
temp/
tmp/
*.tmp
*.bak
*.backup

# Docker
.dockerignore

# 环境变量
.env
.env.local
.env.production

# SSL证书
ssl/
*.pem
*.key
*.crt
*.csr

# 测试覆盖率
coverage/
*.lcov

# Node.js (如果有前端资源)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
