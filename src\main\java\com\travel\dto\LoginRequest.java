package com.travel.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 微信登录请求DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@ApiModel(description = "微信登录请求")
public class LoginRequest {

    @ApiModelProperty(value = "微信登录凭证code", required = true, example = "081234567890")
    @NotBlank(message = "登录凭证不能为空")
    private String code;

    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickname;

    @ApiModelProperty(value = "用户头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @ApiModelProperty(value = "用户地区", example = "北京市")
    private String region;
}
