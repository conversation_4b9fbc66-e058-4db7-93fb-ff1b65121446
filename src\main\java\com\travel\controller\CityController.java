package com.travel.controller;

import com.travel.common.Result;
import com.travel.entity.City;
import com.travel.service.CityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 城市控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/cities")
@Api(tags = "城市管理")
public class CityController {

    @Autowired
    private CityService cityService;

    @GetMapping
    @ApiOperation(value = "获取城市列表", notes = "获取所有可用城市的列表")
    public Result<List<City>> getCities() {
        log.info("获取城市列表请求");
        
        List<City> cities = cityService.getAllCities();
        
        log.info("获取城市列表成功，共{}个城市", cities.size());
        return Result.success("获取成功", cities);
    }

    @GetMapping("/{cityId}")
    @ApiOperation(value = "获取城市详情", notes = "根据城市ID获取城市详细信息")
    public Result<City> getCityDetail(
            @ApiParam(value = "城市ID", required = true, example = "1")
            @PathVariable Integer cityId) {
        
        log.info("获取城市详情请求: cityId={}", cityId);
        
        City city = cityService.getCityById(cityId);
        
        log.info("获取城市详情成功: cityId={}, name={}", cityId, city.getName());
        return Result.success("获取成功", city);
    }
}
