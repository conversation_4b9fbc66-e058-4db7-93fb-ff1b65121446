package com.travel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 城市信息实体
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("city")
@ApiModel(value = "City对象", description = "城市信息表")
public class City {

    @ApiModelProperty(value = "城市ID")
    @TableId(value = "city_id", type = IdType.AUTO)
    private Integer cityId;

    @ApiModelProperty(value = "城市名称")
    private String name;

    @ApiModelProperty(value = "天气信息")
    private String weather;

    @ApiModelProperty(value = "城市Banner图")
    private String bannerUrl;

    @ApiModelProperty(value = "宣传标语")
    private String slogan;
}
