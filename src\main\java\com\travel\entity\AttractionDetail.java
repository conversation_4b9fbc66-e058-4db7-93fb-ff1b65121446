package com.travel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 景区详情实体
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("attraction_detail")
@ApiModel(value = "AttractionDetail对象", description = "景区详情表")
public class AttractionDetail {

    @ApiModelProperty(value = "详情ID")
    @TableId(value = "detail_id", type = IdType.AUTO)
    private Integer detailId;

    @ApiModelProperty(value = "产品ID")
    private Integer productId;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "营业时间")
    private String openHours;

    @ApiModelProperty(value = "景区地图URL")
    private String mapUrl;

    @ApiModelProperty(value = "入口指引")
    private String entranceGuide;

    @ApiModelProperty(value = "讲解视频URL")
    private String videoUrl;

    @ApiModelProperty(value = "视频时长")
    private String videoDuration;
}
