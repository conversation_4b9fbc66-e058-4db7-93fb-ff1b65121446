# 使用OpenJDK 8作为基础镜像
FROM openjdk:8-jre-alpine

# 设置维护者信息
LABEL maintainer="<EMAIL>"
LABEL description="旅游讲解小程序后端服务"
LABEL version="1.0.0"

# 设置工作目录
WORKDIR /app

# 安装必要的包
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建应用用户
RUN addgroup -g 1000 travel && \
    adduser -D -s /bin/sh -u 1000 -G travel travel

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploads && \
    chown -R travel:travel /app

# 复制JAR文件
COPY target/miniprogram-backend-*.jar app.jar

# 设置文件权限
RUN chown travel:travel app.jar

# 切换到应用用户
USER travel

# 暴露端口
EXPOSE 8080

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# 设置应用参数
ENV SPRING_PROFILES_ACTIVE=prod

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/api/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar --spring.profiles.active=$SPRING_PROFILES_ACTIVE"]
