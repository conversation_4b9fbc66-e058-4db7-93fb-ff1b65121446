server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: travel-miniprogram-backend
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    


  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: *************:8080
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Apache License 2.0 | Copyright  2025-[旅游讲解小程序](https://github.com/travel)
    enable-dynamic-parameter: true
    enable-debug: true
    enable-open-api: false
    enable-group: true

# 微信小程序配置
wechat:
  miniprogram:
    app-id: ${WECHAT_APP_ID:your_app_id}
    app-secret: ${WECHAT_APP_SECRET:your_app_secret}
    login-url: https://api.weixin.qq.com/sns/jscode2session
    phone-url: https://api.weixin.qq.com/wxa/business/getuserphonenumber

# JWT配置
jwt:
  secret: ${JWT_SECRET:travel_miniprogram_secret_key_2025}
  expiration: 604800 # 7天，单位秒
  header: Authorization
  prefix: Bearer

# 日志配置
logging:
  level:
    com.travel: debug
    org.springframework.web: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/travel-backend.log
    max-size: 10MB
    max-history: 30

# 文件上传配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/tmp/uploads/}
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,mp3,mp4,pdf

# 接口限流配置
rate-limit:
  enabled: true
  default-limit: 100 # 每分钟请求次数
  login-limit: 10    # 登录接口每分钟请求次数
