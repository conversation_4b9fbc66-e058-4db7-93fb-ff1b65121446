package com.travel.service.impl;

import com.travel.entity.City;
import com.travel.exception.BusinessException;
import com.travel.mapper.CityMapper;
import com.travel.service.CityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 城市服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class CityServiceImpl implements CityService {

    @Autowired
    private CityMapper cityMapper;

    @Override
    public List<City> getAllCities() {
        List<City> cities = cityMapper.selectList(null);
        log.info("获取城市列表成功，共{}个城市", cities.size());
        return cities;
    }

    @Override
    public City getCityById(Integer cityId) {
        City city = cityMapper.selectById(cityId);
        if (city == null) {
            throw new BusinessException("城市不存在");
        }
        log.info("获取城市信息成功: cityId={}, name={}", cityId, city.getName());
        return city;
    }
}
