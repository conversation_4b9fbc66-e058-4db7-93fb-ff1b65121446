package com.travel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.travel.dto.ProductListRequest;
import com.travel.entity.Product;
import com.travel.exception.BusinessException;
import com.travel.mapper.ProductMapper;
import com.travel.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 产品服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductMapper productMapper;

    @Override
    public IPage<Product> getProductList(ProductListRequest request) {
        Page<Product> page = new Page<>(request.getPage(), request.getSize());
        
        // 如果有关键词搜索，使用普通查询
        if (StringUtils.hasText(request.getKeyword())) {
            LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(Product::getName, request.getKeyword())
                    .or()
                    .like(Product::getDescription, request.getKeyword());
            
            if (request.getCityId() != null) {
                queryWrapper.eq(Product::getCityId, request.getCityId());
            }
            
            if (request.getType() != null) {
                queryWrapper.eq(Product::getType, request.getType());
            }
            
            queryWrapper.orderByDesc(Product::getCreatedAt);
            
            IPage<Product> result = productMapper.selectPage(page, queryWrapper);
            log.info("搜索产品列表成功: keyword={}, total={}", request.getKeyword(), result.getTotal());
            return result;
        } else {
            // 使用自定义查询（包含关联信息）
            IPage<Product> result = productMapper.selectProductPage(page, request.getCityId(), request.getType());
            log.info("获取产品列表成功: cityId={}, type={}, total={}", 
                    request.getCityId(), request.getType(), result.getTotal());
            return result;
        }
    }

    @Override
    public Product getProductDetail(Integer productId) {
        Product product = productMapper.selectById(productId);
        if (product == null) {
            throw new BusinessException("产品不存在");
        }
        
        log.info("获取产品详情成功: productId={}, name={}", productId, product.getName());
        return product;
    }
}
