package com.travel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travel.entity.AttractionCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 景区分类Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface AttractionCategoryMapper extends BaseMapper<AttractionCategory> {

    /**
     * 根据产品ID查询分类列表
     */
    @Select("SELECT * FROM attraction_category WHERE product_id = #{productId} ORDER BY sort_order ASC, created_at ASC")
    List<AttractionCategory> selectByProductId(@Param("productId") Integer productId);
}
